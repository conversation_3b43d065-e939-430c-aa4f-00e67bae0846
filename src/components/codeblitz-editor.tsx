import React from "react";
import ReactDOM from "react-dom";

// codeblitz
import {
  App<PERSON><PERSON><PERSON>,
  <PERSON>lot<PERSON><PERSON><PERSON>,
  Slot<PERSON><PERSON><PERSON>,
  SplitPanel,
  BoxPanel,
} from "@codeblitzjs/ide-core/bundle";
import "@codeblitzjs/ide-core/bundle/alex.css";
// codeblitz

// 语法高亮
import "@codeblitzjs/ide-core/languages/html";
import "@codeblitzjs/ide-core/languages/handlebars";
import "@codeblitzjs/ide-core/languages/css";
import "@codeblitzjs/ide-core/languages/scss";
import "@codeblitzjs/ide-core/languages/less";
import "@codeblitzjs/ide-core/languages/javascript";
import "@codeblitzjs/ide-core/languages/typescript";
import "@codeblitzjs/ide-core/languages/json";
// end

// 语言功能
import html from "@codeblitzjs/ide-core/extensions/codeblitz.html-language-features-worker";
import css from "@codeblitzjs/ide-core/extensions/codeblitz.css-language-features-worker";
import typescript from "@codeblitzjs/ide-core/extensions/codeblitz.typescript-language-features-worker";
import json from "@codeblitzjs/ide-core/extensions/codeblitz.json-language-features-worker";

// 布局配置，可根据需要增删模块
export const layoutConfig = {
  [SlotLocation.action]: {
    modules: [""],
  },
  [SlotLocation.left]: {
    modules: ["@opensumi/ide-explorer"],
  },
  [SlotLocation.main]: {
    modules: ["@opensumi/ide-editor"],
  },
  // [SlotLocation.bottom]: {
  //   modules: ['@opensumi/ide-output', '@opensumi/ide-markers'],
  // },
  [SlotLocation.statusBar]: {
    modules: ["@opensumi/ide-status-bar"],
  },
};

// 界面布局组件，可根据需要调整
const LayoutComponent = () => (
  <BoxPanel direction="top-to-bottom">
    <SplitPanel overflow="hidden" id="main-horizontal" flex={1}>
      <SlotRenderer slot="left" minResize={220} minSize={49} />
      <SplitPanel
        id="main-vertical"
        minResize={300}
        flexGrow={1}
        direction="top-to-bottom"
      >
        <SlotRenderer flex={2} flexGrow={1} minResize={200} slot="main" />
        {/* <SlotRenderer flex={1} minResize={160} slot="bottom" /> */}
      </SplitPanel>
    </SplitPanel>
    <SlotRenderer slot="statusBar" />
  </BoxPanel>
);

const App: React.FC = () => {
  const [key, setKey] = React.useState(0);

  return (
    <div style={{ width: "100%", height: "100%", padding: 8 }}>
      <div style={{ height: 40 }}>
        <button onClick={() => setKey((k) => k + 1)}>重置</button>
      </div>
      <div style={{ height: "calc(100% - 40px)", width: "50%" }}>
        {key === 0 && (
          <AppRenderer
            key={key}
            appConfig={{
              // 工作空间目录，最好确保不同项目名称不同，如 group/repository 的形式，工作空间目录会挂载到 /workspace 根目录下
              workspaceDir: "playground",
              layoutConfig,
              layoutComponent: LayoutComponent,
              // 默认偏好设置
              defaultPreferences: {
                "general.theme": "opensumi-light",
              },
              // 左侧面板默认宽度
              panelSizes: {
                [SlotLocation.left]: 220,
              },
              // 扩展
              extensionMetadata: [html, css, typescript, json],
            }}
            runtimeConfig={{
              // 禁止就改文件树，此时无法新增、删除、重命名文件
              disableModifyFileTree: true,
              // 默认打开文件
              defaultOpenFile: "main.js",
              workspace: {
                // 文件系统配置
                filesystem: {
                  fs: "FileIndexSystem",
                  options: {
                    // 初始全量文件索引
                    requestFileIndex() {
                      return Promise.resolve({
                        "main.html": '<div id="root"></div>',
                        "main.css": "body {}",
                        "main.js": 'console.log("main")',
                      });
                    },
                  },
                },
                // 文件保存事件
                onDidSaveTextDocument(e) {
                  console.log(e);
                },
              },
            }}
          />
        )}
      </div>
    </div>
  );
};
