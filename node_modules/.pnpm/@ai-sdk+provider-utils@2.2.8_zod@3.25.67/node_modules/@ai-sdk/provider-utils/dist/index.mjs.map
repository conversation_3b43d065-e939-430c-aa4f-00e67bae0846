{"version": 3, "sources": ["../src/combine-headers.ts", "../src/convert-async-iterator-to-readable-stream.ts", "../src/delay.ts", "../src/event-source-parser-stream.ts", "../src/extract-response-headers.ts", "../src/generate-id.ts", "../src/get-error-message.ts", "../src/get-from-api.ts", "../src/remove-undefined-entries.ts", "../src/is-abort-error.ts", "../src/load-api-key.ts", "../src/load-optional-setting.ts", "../src/load-setting.ts", "../src/parse-json.ts", "../src/validate-types.ts", "../src/validator.ts", "../src/parse-provider-options.ts", "../src/post-to-api.ts", "../src/resolve.ts", "../src/response-handler.ts", "../src/uint8-utils.ts", "../src/without-trailing-slash.ts"], "sourcesContent": ["export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @returns A Promise that resolves after the specified delay\n */\nexport async function delay(delayInMs?: number | null): Promise<void> {\n  return delayInMs == null\n    ? Promise.resolve()\n    : new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "export type EventSourceChunk = {\n  event: string | undefined;\n  data: string;\n  id?: string;\n  retry?: number;\n};\n\nexport function createEventSourceParserStream() {\n  let buffer = '';\n  let event: string | undefined = undefined;\n  let data: string[] = [];\n  let lastEventId: string | undefined = undefined;\n  let retry: number | undefined = undefined;\n\n  function parseLine(\n    line: string,\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    // Empty line means dispatch the event\n    if (line === '') {\n      dispatchEvent(controller);\n      return;\n    }\n\n    // Comments start with colon\n    if (line.startsWith(':')) {\n      return;\n    }\n\n    // Field parsing\n    const colonIndex = line.indexOf(':');\n    if (colonIndex === -1) {\n      // field with no value\n      handleField(line, '');\n      return;\n    }\n\n    const field = line.slice(0, colonIndex);\n    // If there's a space after the colon, it should be ignored\n    const valueStart = colonIndex + 1;\n    const value =\n      valueStart < line.length && line[valueStart] === ' '\n        ? line.slice(valueStart + 1)\n        : line.slice(valueStart);\n\n    handleField(field, value);\n  }\n\n  function dispatchEvent(\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join('\\n'),\n        id: lastEventId,\n        retry,\n      });\n\n      // Reset data but keep lastEventId as per spec\n      data = [];\n      event = undefined;\n      retry = undefined;\n    }\n  }\n\n  function handleField(field: string, value: string) {\n    switch (field) {\n      case 'event':\n        event = value;\n        break;\n      case 'data':\n        data.push(value);\n        break;\n      case 'id':\n        lastEventId = value;\n        break;\n      case 'retry':\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n\n  return new TransformStream<string, EventSourceChunk>({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n\n      buffer = incompleteLine;\n\n      // using for loop for performance\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    },\n  });\n}\n\n// performance: send in already scanned buffer separately, do not scan again\nfunction splitLines(buffer: string, chunk: string) {\n  const lines: Array<string> = [];\n  let currentLine = buffer;\n\n  // using for loop for performance\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n\n    // order is performance-optimized\n    if (char === '\\n') {\n      // Standalone LF\n      lines.push(currentLine);\n      currentLine = '';\n    } else if (char === '\\r') {\n      lines.push(currentLine);\n      currentLine = '';\n      if (chunk[i] === '\\n') {\n        i++; // CRLF case: Skip the LF character\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n\n  return { lines, incompleteLine: currentLine };\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(\n  response: Response,\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { customAlphabet } from 'nanoid/non-secure';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNon-secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Default: ''.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\n// TODO 5.0 breaking change: change the return type to IDGenerator\nexport const createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): ((size?: number) => string) => {\n  const generator = customAlphabet(alphabet, defaultSize);\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return size => `${prefix}${separator}${generator(size)}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IDGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs. Not secure.\n\n@param size - The size of the ID to generate. Default: 16.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { FetchFunction } from './fetch-function';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\nimport { isAbortError } from './is-abort-error';\nimport { extractResponseHeaders } from './extract-response-headers';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n      if (cause != null) {\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {},\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  JSONParseError,\n  <PERSON>SO<PERSON>V<PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport SecureJSON from 'secure-json-parse';\nimport { ZodSchema } from 'zod';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): JSONValue;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {T} - The parsed object.\n */\nexport function parseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): T;\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): T {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | { success: false; error: JSONParseError | TypeValidationError };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {object} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): ParseResult<JSONValue>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeParseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): ParseResult<T>;\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): ParseResult<T> {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    const validationResult = safeValidateTypes({ value, schema });\n\n    return validationResult.success\n      ? { ...validationResult, rawValue: value }\n      : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    SecureJSON.parse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { z } from 'zod';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {T} - The typed object.\n */\nexport function validateTypes<T>({\n  value,\n  schema: inputSchema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}): T {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeValidateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: TypeValidationError } {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as T };\n    }\n\n    const result = validator.validate(value);\n\n    if (result.success) {\n      return result;\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n    };\n  }\n}\n", "import { z } from 'zod';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (value: unknown) => ValidationResult<OBJECT>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?: undefined | ((value: unknown) => ValidationResult<OBJECT>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : zodValidator(value);\n}\n\nexport function zodValidator<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return validator(value => {\n    const result = zodSchema.safeParse(value);\n    return result.success\n      ? { success: true, value: result.data }\n      : { success: false, error: result.error };\n  });\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod';\n\nexport function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.ZodSchema<T>;\n}): T | undefined {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponse<PERSON>and<PERSON>,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    // unwrap original error when fetch failed (for easier debugging):\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n\n      if (cause != null) {\n        // Failed to connect to server:\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true, // retry when network error\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodSchema } from 'zod';\nimport {\n  createEventSourceParserStream,\n  EventSourceChunk,\n} from './event-source-parser-stream';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodSchema<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(createEventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<EventSourceChunk, ParseResult<T>>({\n            transform({ data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n\n              controller.enqueue(\n                safeParseJSON({\n                  text: data,\n                  schema: chunkSchema,\n                }),\n              );\n            },\n          }),\n        ),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodSchema<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise CloudFlare will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n"], "mappings": ";AAAO,SAAS,kBACX,SACiC;AACpC,SAAO,QAAQ;AAAA,IACb,CAAC,iBAAiB,oBAAoB;AAAA,MACpC,GAAG;AAAA,MACH,GAAI,0CAAkB,CAAC;AAAA,IACzB;AAAA,IACA,CAAC;AAAA,EACH;AACF;;;ACHO,SAAS,qCACd,UACmB;AACnB,SAAO,IAAI,eAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAO3B,MAAM,KAAK,YAAY;AACrB,UAAI;AACF,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,SAAS,KAAK;AAC5C,YAAI,MAAM;AACR,qBAAW,MAAM;AAAA,QACnB,OAAO;AACL,qBAAW,QAAQ,KAAK;AAAA,QAC1B;AAAA,MACF,SAAS,OAAO;AACd,mBAAW,MAAM,KAAK;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS;AAAA,IAAC;AAAA,EACZ,CAAC;AACH;;;AC7BA,eAAsB,MAAM,WAA0C;AACpE,SAAO,aAAa,OAChB,QAAQ,QAAQ,IAChB,IAAI,QAAQ,CAAAA,aAAW,WAAWA,UAAS,SAAS,CAAC;AAC3D;;;ACFO,SAAS,gCAAgC;AAC9C,MAAI,SAAS;AACb,MAAI,QAA4B;AAChC,MAAI,OAAiB,CAAC;AACtB,MAAI,cAAkC;AACtC,MAAI,QAA4B;AAEhC,WAAS,UACP,MACA,YACA;AAEA,QAAI,SAAS,IAAI;AACf,oBAAc,UAAU;AACxB;AAAA,IACF;AAGA,QAAI,KAAK,WAAW,GAAG,GAAG;AACxB;AAAA,IACF;AAGA,UAAM,aAAa,KAAK,QAAQ,GAAG;AACnC,QAAI,eAAe,IAAI;AAErB,kBAAY,MAAM,EAAE;AACpB;AAAA,IACF;AAEA,UAAM,QAAQ,KAAK,MAAM,GAAG,UAAU;AAEtC,UAAM,aAAa,aAAa;AAChC,UAAM,QACJ,aAAa,KAAK,UAAU,KAAK,UAAU,MAAM,MAC7C,KAAK,MAAM,aAAa,CAAC,IACzB,KAAK,MAAM,UAAU;AAE3B,gBAAY,OAAO,KAAK;AAAA,EAC1B;AAEA,WAAS,cACP,YACA;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,iBAAW,QAAQ;AAAA,QACjB;AAAA,QACA,MAAM,KAAK,KAAK,IAAI;AAAA,QACpB,IAAI;AAAA,QACJ;AAAA,MACF,CAAC;AAGD,aAAO,CAAC;AACR,cAAQ;AACR,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,WAAS,YAAY,OAAe,OAAe;AACjD,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,gBAAQ;AACR;AAAA,MACF,KAAK;AACH,aAAK,KAAK,KAAK;AACf;AAAA,MACF,KAAK;AACH,sBAAc;AACd;AAAA,MACF,KAAK;AACH,cAAM,cAAc,SAAS,OAAO,EAAE;AACtC,YAAI,CAAC,MAAM,WAAW,GAAG;AACvB,kBAAQ;AAAA,QACV;AACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO,IAAI,gBAA0C;AAAA,IACnD,UAAU,OAAO,YAAY;AAC3B,YAAM,EAAE,OAAO,eAAe,IAAI,WAAW,QAAQ,KAAK;AAE1D,eAAS;AAGT,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAU,MAAM,CAAC,GAAG,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,IAEA,MAAM,YAAY;AAChB,gBAAU,QAAQ,UAAU;AAC5B,oBAAc,UAAU;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AAGA,SAAS,WAAW,QAAgB,OAAe;AACjD,QAAM,QAAuB,CAAC;AAC9B,MAAI,cAAc;AAGlB,WAAS,IAAI,GAAG,IAAI,MAAM,UAAU;AAClC,UAAM,OAAO,MAAM,GAAG;AAGtB,QAAI,SAAS,MAAM;AAEjB,YAAM,KAAK,WAAW;AACtB,oBAAc;AAAA,IAChB,WAAW,SAAS,MAAM;AACxB,YAAM,KAAK,WAAW;AACtB,oBAAc;AACd,UAAI,MAAM,CAAC,MAAM,MAAM;AACrB;AAAA,MACF;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,EAAE,OAAO,gBAAgB,YAAY;AAC9C;;;AC7HO,SAAS,uBACd,UACwB;AACxB,QAAM,UAAkC,CAAC;AACzC,WAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,YAAQ,GAAG,IAAI;AAAA,EACjB,CAAC;AACD,SAAO;AACT;;;ACdA,SAAS,4BAA4B;AACrC,SAAS,sBAAsB;AAaxB,IAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA,MAAM,cAAc;AAAA,EACpB,WAAW;AAAA,EACX,YAAY;AACd,IAKI,CAAC,MAAmC;AACtC,QAAM,YAAY,eAAe,UAAU,WAAW;AAEtD,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AAGA,MAAI,SAAS,SAAS,SAAS,GAAG;AAChC,UAAM,IAAI,qBAAqB;AAAA,MAC7B,UAAU;AAAA,MACV,SAAS,kBAAkB,SAAS,uCAAuC,QAAQ;AAAA,IACrF,CAAC;AAAA,EACH;AAEA,SAAO,UAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,IAAI,CAAC;AACxD;AAYO,IAAM,aAAa,kBAAkB;;;ACpDrC,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;;;ACdA,SAAS,oBAAoB;;;ACKtB,SAAS,uBACd,QACmB;AACnB,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,SAAS,IAAI;AAAA,EAChE;AACF;;;ACXO,SAAS,aAAa,OAAgC;AAC3D,SACE,iBAAiB,UAChB,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAEnD;;;AFGA,IAAM,mBAAmB,MAAM,WAAW;AAEnC,IAAM,aAAa,OAAU;AAAA,EAClC;AAAA,EACA,UAAU,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,iBAAiB;AAC3B,MAOM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;AAAA,MAChC,QAAQ;AAAA,MACR,SAAS,uBAAuB,OAAO;AAAA,MACvC,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;AAAA,UAC7C;AAAA,UACA;AAAA,UACA,mBAAmB,CAAC;AAAA,QACtB,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAK,aAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAEA,cAAM,IAAI,aAAa;AAAA,UACrB,SAAS;AAAA,UACT,OAAO;AAAA,UACP,YAAY,SAAS;AAAA,UACrB;AAAA,UACA;AAAA,UACA,mBAAmB,CAAC;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,YAAM,iBAAiB;AAAA,IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;AAAA,QACrC;AAAA,QACA;AAAA,QACA,mBAAmB,CAAC;AAAA,MACtB,CAAC;AAAA,IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAK,aAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,IAAI,aAAa;AAAA,QACrB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,mBAAmB,CAAC;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;AAAA,IACR;AAEA,QAAI,iBAAiB,aAAa,MAAM,YAAY,gBAAgB;AAClE,YAAM,QAAS,MAAc;AAC7B,UAAI,SAAS,MAAM;AACjB,cAAM,IAAI,aAAa;AAAA,UACrB,SAAS,0BAA0B,MAAM,OAAO;AAAA,UAChD;AAAA,UACA;AAAA,UACA,aAAa;AAAA,UACb,mBAAmB,CAAC;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AACF;;;AG1GA,SAAS,uBAAuB;AAEzB,SAAS,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB;AACF,GAKW;AACT,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB;AAAA,IACvF,CAAC;AAAA,EACH;AAEA,WAAS,QAAQ,IAAI,uBAAuB;AAE5C,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB,sBAAsB,uBAAuB;AAAA,IACpI,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,gBAAgB;AAAA,MACxB,SAAS,GAAG,WAAW,+CAA+C,uBAAuB;AAAA,IAC/F,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;ACrCO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AACF,GAGuB;AACrB,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,gBAAgB,QAAQ,OAAO,YAAY,aAAa;AAC1D,WAAO;AAAA,EACT;AAEA,iBAAe,QAAQ,IAAI,uBAAuB;AAElD,MAAI,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU;AAC5D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC7BA,SAAS,wBAAwB;AAW1B,SAAS,YAAY;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKW;AACT,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,iBAAiB;AAAA,MACzB,SAAS,GAAG,WAAW;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,iBAAiB;AAAA,MACzB,SACE,GAAG,WAAW,2CACQ,WAAW;AAAA,IAErC,CAAC;AAAA,EACH;AAEA,iBAAe,QAAQ,IAAI,uBAAuB;AAElD,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,iBAAiB;AAAA,MACzB,SACE,GAAG,WAAW,2CACQ,WAAW,sBACvB,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,iBAAiB,UAAU;AACpC,UAAM,IAAI,iBAAiB;AAAA,MACzB,SACE,GAAG,WAAW,+CACM,uBAAuB;AAAA,IAC/C,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AC7DA;AAAA,EACE;AAAA,EAEA,uBAAAC;AAAA,OACK;AACP,OAAO,gBAAgB;;;ACLvB,SAAS,2BAA2B;;;ACK7B,IAAM,kBAAkB,OAAO,IAAI,qBAAqB;AAwBxD,SAAS,UACd,UACmB;AACnB,SAAO,EAAE,CAAC,eAAe,GAAG,MAAM,SAAS;AAC7C;AAEO,SAAS,YAAY,OAAoC;AAC9D,SACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,MAAM,eAAe,MAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,OACmB;AACnB,SAAO,YAAY,KAAK,IAAI,QAAQ,aAAa,KAAK;AACxD;AAEO,SAAS,aACd,WACmB;AACnB,SAAO,UAAU,WAAS;AACxB,UAAM,SAAS,UAAU,UAAU,KAAK;AACxC,WAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;AAAA,EAC5C,CAAC;AACH;;;AD/CO,SAAS,cAAiB;AAAA,EAC/B;AAAA,EACA,QAAQ;AACV,GAGM;AACJ,QAAM,SAAS,kBAAkB,EAAE,OAAO,QAAQ,YAAY,CAAC;AAE/D,MAAI,CAAC,OAAO,SAAS;AACnB,UAAM,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,EAC/D;AAEA,SAAO,OAAO;AAChB;AAWO,SAAS,kBAAqB;AAAA,EACnC;AAAA,EACA;AACF,GAKmD;AACjD,QAAMC,aAAY,YAAY,MAAM;AAEpC,MAAI;AACF,QAAIA,WAAU,YAAY,MAAM;AAC9B,aAAO,EAAE,SAAS,MAAM,MAAkB;AAAA,IAC5C;AAEA,UAAM,SAASA,WAAU,SAAS,KAAK;AAEvC,QAAI,OAAO,SAAS;AAClB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,IAChE;AAAA,EACF,SAAS,OAAO;AACd,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,oBAAoB,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC;AAAA,IACzD;AAAA,EACF;AACF;;;ADtCO,SAAS,UAAa;AAAA,EAC3B;AAAA,EACA;AACF,GAGM;AACJ,MAAI;AACF,UAAM,QAAQ,WAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AAEA,WAAO,cAAc,EAAE,OAAO,OAAO,CAAC;AAAA,EACxC,SAAS,OAAO;AACd,QACE,eAAe,WAAW,KAAK,KAC/BC,qBAAoB,WAAW,KAAK,GACpC;AACA,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;AAAA,EACjD;AACF;AA4BO,SAAS,cAAiB;AAAA,EAC/B;AAAA,EACA;AACF,GAGmB;AACjB,MAAI;AACF,UAAM,QAAQ,WAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO,EAAE,SAAS,MAAM,OAAmB,UAAU,MAAM;AAAA,IAC7D;AAEA,UAAM,mBAAmB,kBAAkB,EAAE,OAAO,OAAO,CAAC;AAE5D,WAAO,iBAAiB,UACpB,EAAE,GAAG,kBAAkB,UAAU,MAAM,IACvC;AAAA,EACN,SAAS,OAAO;AACd,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,eAAe,WAAW,KAAK,IAClC,QACA,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;AAAA,IAC/C;AAAA,EACF;AACF;AAEO,SAAS,eAAe,OAAwB;AACrD,MAAI;AACF,eAAW,MAAM,KAAK;AACtB,WAAO;AAAA,EACT,SAAQ;AACN,WAAO;AAAA,EACT;AACF;;;AGzHA,SAAS,wBAAAC,6BAA4B;AAI9B,SAAS,qBAAwB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,GAIkB;AAChB,OAAI,mDAAkB,cAAa,MAAM;AACvC,WAAO;AAAA,EACT;AAEA,QAAM,wBAAwB,kBAAkB;AAAA,IAC9C,OAAO,gBAAgB,QAAQ;AAAA,IAC/B;AAAA,EACF,CAAC;AAED,MAAI,CAAC,sBAAsB,SAAS;AAClC,UAAM,IAAIC,sBAAqB;AAAA,MAC7B,UAAU;AAAA,MACV,SAAS,WAAW,QAAQ;AAAA,MAC5B,OAAO,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH;AAEA,SAAO,sBAAsB;AAC/B;;;AC/BA,SAAS,gBAAAC,qBAAoB;AAQ7B,IAAMC,oBAAmB,MAAM,WAAW;AAEnC,IAAM,gBAAgB,OAAU;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MASE,UAAU;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AAAA,EACA,MAAM;AAAA,IACJ,SAAS,KAAK,UAAU,IAAI;AAAA,IAC5B,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAEI,IAAM,oBAAoB,OAAU;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MASE,UAAU;AAAA,EACR;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,QAAQ,OAAO,YAAa,SAAiB,QAAQ,CAAC;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAEI,IAAM,YAAY,OAAU;AAAA,EACjC;AAAA,EACA,UAAU,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQA,kBAAiB;AAC3B,MAWM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;AAAA,MAChC,QAAQ;AAAA,MACR,SAAS,uBAAuB,OAAO;AAAA,MACvC,MAAM,KAAK;AAAA,MACX,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;AAAA,UAC7C;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAKC,cAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAEA,cAAM,IAAIA,cAAa;AAAA,UACrB,SAAS;AAAA,UACT,OAAO;AAAA,UACP,YAAY,SAAS;AAAA,UACrB;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B,CAAC;AAAA,MACH;AAEA,YAAM,iBAAiB;AAAA,IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;AAAA,QACrC;AAAA,QACA;AAAA,QACA,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAKA,cAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,IAAIA,cAAa;AAAA,QACrB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;AAAA,IACR;AAGA,QAAI,iBAAiB,aAAa,MAAM,YAAY,gBAAgB;AAClE,YAAM,QAAS,MAAc;AAE7B,UAAI,SAAS,MAAM;AAEjB,cAAM,IAAIA,cAAa;AAAA,UACrB,SAAS,0BAA0B,MAAM,OAAO;AAAA,UAChD;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,UACxB,aAAa;AAAA;AAAA,QACf,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AACF;;;ACxKA,eAAsB,QAAW,OAAkC;AAEjE,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAS,MAAmB;AAAA,EAC9B;AAGA,SAAO,QAAQ,QAAQ,KAAU;AACnC;;;AClBA,SAAS,gBAAAC,eAAc,8BAA8B;AAmB9C,IAAM,iCACX,CAAI;AAAA,EACF;AAAA,EACA;AAAA,EACA;AACF,MAKA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,kBAAkB,uBAAuB,QAAQ;AAGvD,MAAI,aAAa,KAAK,MAAM,IAAI;AAC9B,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAIC,cAAa;AAAA,QACtB,SAAS,SAAS;AAAA,QAClB;AAAA,QACA;AAAA,QACA,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,aAAa,2CAAc;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF;AAGA,MAAI;AACF,UAAM,cAAc,UAAU;AAAA,MAC5B,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAIA,cAAa;AAAA,QACtB,SAAS,eAAe,WAAW;AAAA,QACnC;AAAA,QACA;AAAA,QACA,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,aAAa,2CAAc,UAAU;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF,SAAS,YAAY;AACnB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAIA,cAAa;AAAA,QACtB,SAAS,SAAS;AAAA,QAClB;AAAA,QACA;AAAA,QACA,YAAY,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,QACA,aAAa,2CAAc;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEK,IAAM,mCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;AAAA,EACrC;AAEA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS,KACb,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,8BAA8B,CAAC,EAC3C;AAAA,MACC,IAAI,gBAAkD;AAAA,QACpD,UAAU,EAAE,KAAK,GAAG,YAAY;AAE9B,cAAI,SAAS,UAAU;AACrB;AAAA,UACF;AAEA,qBAAW;AAAA,YACT,cAAc;AAAA,cACZ,MAAM;AAAA,cACN,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACJ;AACF;AAEK,IAAM,kCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS;AAEb,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS,KAAK,YAAY,IAAI,kBAAkB,CAAC,EAAE;AAAA,MACxD,IAAI,gBAAwC;AAAA,QAC1C,UAAU,WAAW,YAAY;AAC/B,cAAI,UAAU,SAAS,IAAI,GAAG;AAC5B,uBAAW;AAAA,cACT,cAAc;AAAA,gBACZ,MAAM,SAAS;AAAA,gBACf,QAAQ;AAAA,cACV,CAAC;AAAA,YACH;AACA,qBAAS;AAAA,UACX,OAAO;AACL,sBAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEK,IAAM,4BACX,CAAI,mBACJ,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,QAAM,eAAe,cAAc;AAAA,IACjC,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAIA,cAAa;AAAA,MACrB,SAAS;AAAA,MACT,OAAO,aAAa;AAAA,MACpB,YAAY,SAAS;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,aAAa;AAAA,IACpB,UAAU,aAAa;AAAA,EACzB;AACF;AAEK,IAAM,8BACX,MACA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAIA,cAAa;AAAA,MACrB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,MAAI;AACF,UAAM,SAAS,MAAM,SAAS,YAAY;AAC1C,WAAO;AAAA,MACL;AAAA,MACA,OAAO,IAAI,WAAW,MAAM;AAAA,IAC9B;AAAA,EACF,SAAS,OAAO;AACd,UAAM,IAAIA,cAAa;AAAA,MACrB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,MACA,cAAc;AAAA,MACd,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEK,IAAM,uCACX,MACA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AACvD,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,SAAO;AAAA,IACL;AAAA,IACA,OAAO,IAAIA,cAAa;AAAA,MACtB,SAAS,SAAS;AAAA,MAClB;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AAAA,MACrB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AC5OF,IAAM,EAAE,MAAM,KAAK,IAAI;AAEhB,SAAS,0BAA0B,cAAsB;AAC9D,QAAM,YAAY,aAAa,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACnE,QAAM,eAAe,KAAK,SAAS;AACnC,SAAO,WAAW,KAAK,cAAc,UAAQ,KAAK,YAAY,CAAC,CAAE;AACnE;AAEO,SAAS,0BAA0B,OAA2B;AACnE,MAAI,eAAe;AAInB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAgB,OAAO,cAAc,MAAM,CAAC,CAAC;AAAA,EAC/C;AAEA,SAAO,KAAK,YAAY;AAC1B;;;ACrBO,SAAS,qBAAqB,KAAyB;AAC5D,SAAO,2BAAK,QAAQ,OAAO;AAC7B;", "names": ["resolve", "TypeValidationError", "validator", "TypeValidationError", "InvalidArgumentError", "InvalidArgumentError", "APICallError", "getOriginalFetch", "APICallError", "APICallError", "APICallError"]}