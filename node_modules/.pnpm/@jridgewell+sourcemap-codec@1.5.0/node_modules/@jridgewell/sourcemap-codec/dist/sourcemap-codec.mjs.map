{"version": 3, "file": "sourcemap-codec.mjs", "sources": ["../src/vlq.ts", "../src/strings.ts", "../src/scopes.ts", "../src/sourcemap-codec.ts"], "sourcesContent": ["import type { StringReader, StringWriter } from './strings';\n\nexport const comma = ','.charCodeAt(0);\nexport const semicolon = ';'.charCodeAt(0);\n\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\n\nfor (let i = 0; i < chars.length; i++) {\n  const c = chars.charCodeAt(i);\n  intToChar[i] = c;\n  charToInt[c] = i;\n}\n\nexport function decodeInteger(reader: StringReader, relative: number): number {\n  let value = 0;\n  let shift = 0;\n  let integer = 0;\n\n  do {\n    const c = reader.next();\n    integer = charToInt[c];\n    value |= (integer & 31) << shift;\n    shift += 5;\n  } while (integer & 32);\n\n  const shouldNegate = value & 1;\n  value >>>= 1;\n\n  if (shouldNegate) {\n    value = -0x80000000 | -value;\n  }\n\n  return relative + value;\n}\n\nexport function encodeInteger(builder: StringWriter, num: number, relative: number): number {\n  let delta = num - relative;\n\n  delta = delta < 0 ? (-delta << 1) | 1 : delta << 1;\n  do {\n    let clamped = delta & 0b011111;\n    delta >>>= 5;\n    if (delta > 0) clamped |= 0b100000;\n    builder.write(intToChar[clamped]);\n  } while (delta > 0);\n\n  return num;\n}\n\nexport function hasMoreVlq(reader: StringReader, max: number) {\n  if (reader.pos >= max) return false;\n  return reader.peek() !== comma;\n}\n", "const bufLength = 1024 * 16;\n\n// Provide a fallback for older environments.\nconst td =\n  typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n    ? {\n        decode(buf: Uint8Array): string {\n          const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n          return out.toString();\n        },\n      }\n    : {\n        decode(buf: Uint8Array): string {\n          let out = '';\n          for (let i = 0; i < buf.length; i++) {\n            out += String.fromCharCode(buf[i]);\n          }\n          return out;\n        },\n      };\n\nexport class StringWriter {\n  pos = 0;\n  private out = '';\n  private buffer = new Uint8Array(bufLength);\n\n  write(v: number): void {\n    const { buffer } = this;\n    buffer[this.pos++] = v;\n    if (this.pos === bufLength) {\n      this.out += td.decode(buffer);\n      this.pos = 0;\n    }\n  }\n\n  flush(): string {\n    const { buffer, out, pos } = this;\n    return pos > 0 ? out + td.decode(buffer.subarray(0, pos)) : out;\n  }\n}\n\nexport class StringReader {\n  pos = 0;\n  private declare buffer: string;\n\n  constructor(buffer: string) {\n    this.buffer = buffer;\n  }\n\n  next(): number {\n    return this.buffer.charCodeAt(this.pos++);\n  }\n\n  peek(): number {\n    return this.buffer.charCodeAt(this.pos);\n  }\n\n  indexOf(char: string): number {\n    const { buffer, pos } = this;\n    const idx = buffer.indexOf(char, pos);\n    return idx === -1 ? buffer.length : idx;\n  }\n}\n", "import { String<PERSON>ead<PERSON>, StringWriter } from './strings';\nimport { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\n\nconst EMPTY: any[] = [];\n\ntype Line = number;\ntype Column = number;\ntype Kind = number;\ntype Name = number;\ntype Var = number;\ntype SourcesIndex = number;\ntype ScopesIndex = number;\n\ntype Mix<A, B, O> = (A & O) | (B & O);\n\nexport type OriginalScope = Mix<\n  [Line, Column, Line, Column, Kind],\n  [Line, Column, Line, Column, Kind, Name],\n  { vars: Var[] }\n>;\n\nexport type GeneratedRange = Mix<\n  [Line, Column, Line, Column],\n  [Line, Column, Line, Column, SourcesIndex, ScopesIndex],\n  {\n    callsite: CallSite | null;\n    bindings: Binding[];\n    isScope: boolean;\n  }\n>;\nexport type CallSite = [SourcesIndex, Line, Column];\ntype Binding = BindingExpressionRange[];\nexport type BindingExpressionRange = [Name] | [Name, Line, Column];\n\nexport function decodeOriginalScopes(input: string): OriginalScope[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const scopes: OriginalScope[] = [];\n  const stack: OriginalScope[] = [];\n  let line = 0;\n\n  for (; reader.pos < length; reader.pos++) {\n    line = decodeInteger(reader, line);\n    const column = decodeInteger(reader, 0);\n\n    if (!hasMoreVlq(reader, length)) {\n      const last = stack.pop()!;\n      last[2] = line;\n      last[3] = column;\n      continue;\n    }\n\n    const kind = decodeInteger(reader, 0);\n    const fields = decodeInteger(reader, 0);\n    const hasName = fields & 0b0001;\n\n    const scope: OriginalScope = (\n      hasName ? [line, column, 0, 0, kind, decodeInteger(reader, 0)] : [line, column, 0, 0, kind]\n    ) as OriginalScope;\n\n    let vars: Var[] = EMPTY;\n    if (hasMoreVlq(reader, length)) {\n      vars = [];\n      do {\n        const varsIndex = decodeInteger(reader, 0);\n        vars.push(varsIndex);\n      } while (hasMoreVlq(reader, length));\n    }\n    scope.vars = vars;\n\n    scopes.push(scope);\n    stack.push(scope);\n  }\n\n  return scopes;\n}\n\nexport function encodeOriginalScopes(scopes: OriginalScope[]): string {\n  const writer = new StringWriter();\n\n  for (let i = 0; i < scopes.length; ) {\n    i = _encodeOriginalScopes(scopes, i, writer, [0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeOriginalScopes(\n  scopes: OriginalScope[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenColumn\n  ],\n): number {\n  const scope = scopes[index];\n  const { 0: startLine, 1: startColumn, 2: endLine, 3: endColumn, 4: kind, vars } = scope;\n\n  if (index > 0) writer.write(comma);\n\n  state[0] = encodeInteger(writer, startLine, state[0]);\n  encodeInteger(writer, startColumn, 0);\n  encodeInteger(writer, kind, 0);\n\n  const fields = scope.length === 6 ? 0b0001 : 0;\n  encodeInteger(writer, fields, 0);\n  if (scope.length === 6) encodeInteger(writer, scope[5], 0);\n\n  for (const v of vars) {\n    encodeInteger(writer, v, 0);\n  }\n\n  for (index++; index < scopes.length; ) {\n    const next = scopes[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeOriginalScopes(scopes, index, writer, state);\n  }\n\n  writer.write(comma);\n  state[0] = encodeInteger(writer, endLine, state[0]);\n  encodeInteger(writer, endColumn, 0);\n\n  return index;\n}\n\nexport function decodeGeneratedRanges(input: string): GeneratedRange[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const ranges: GeneratedRange[] = [];\n  const stack: GeneratedRange[] = [];\n\n  let genLine = 0;\n  let definitionSourcesIndex = 0;\n  let definitionScopeIndex = 0;\n  let callsiteSourcesIndex = 0;\n  let callsiteLine = 0;\n  let callsiteColumn = 0;\n  let bindingLine = 0;\n  let bindingColumn = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    let genColumn = 0;\n\n    for (; reader.pos < semi; reader.pos++) {\n      genColumn = decodeInteger(reader, genColumn);\n\n      if (!hasMoreVlq(reader, semi)) {\n        const last = stack.pop()!;\n        last[2] = genLine;\n        last[3] = genColumn;\n        continue;\n      }\n\n      const fields = decodeInteger(reader, 0);\n      const hasDefinition = fields & 0b0001;\n      const hasCallsite = fields & 0b0010;\n      const hasScope = fields & 0b0100;\n\n      let callsite: CallSite | null = null;\n      let bindings: Binding[] = EMPTY;\n      let range: GeneratedRange;\n      if (hasDefinition) {\n        const defSourcesIndex = decodeInteger(reader, definitionSourcesIndex);\n        definitionScopeIndex = decodeInteger(\n          reader,\n          definitionSourcesIndex === defSourcesIndex ? definitionScopeIndex : 0,\n        );\n\n        definitionSourcesIndex = defSourcesIndex;\n        range = [genLine, genColumn, 0, 0, defSourcesIndex, definitionScopeIndex] as GeneratedRange;\n      } else {\n        range = [genLine, genColumn, 0, 0] as GeneratedRange;\n      }\n\n      range.isScope = !!hasScope;\n\n      if (hasCallsite) {\n        const prevCsi = callsiteSourcesIndex;\n        const prevLine = callsiteLine;\n        callsiteSourcesIndex = decodeInteger(reader, callsiteSourcesIndex);\n        const sameSource = prevCsi === callsiteSourcesIndex;\n        callsiteLine = decodeInteger(reader, sameSource ? callsiteLine : 0);\n        callsiteColumn = decodeInteger(\n          reader,\n          sameSource && prevLine === callsiteLine ? callsiteColumn : 0,\n        );\n\n        callsite = [callsiteSourcesIndex, callsiteLine, callsiteColumn];\n      }\n      range.callsite = callsite;\n\n      if (hasMoreVlq(reader, semi)) {\n        bindings = [];\n        do {\n          bindingLine = genLine;\n          bindingColumn = genColumn;\n          const expressionsCount = decodeInteger(reader, 0);\n          let expressionRanges: BindingExpressionRange[];\n          if (expressionsCount < -1) {\n            expressionRanges = [[decodeInteger(reader, 0)]];\n            for (let i = -1; i > expressionsCount; i--) {\n              const prevBl = bindingLine;\n              bindingLine = decodeInteger(reader, bindingLine);\n              bindingColumn = decodeInteger(reader, bindingLine === prevBl ? bindingColumn : 0);\n              const expression = decodeInteger(reader, 0);\n              expressionRanges.push([expression, bindingLine, bindingColumn]);\n            }\n          } else {\n            expressionRanges = [[expressionsCount]];\n          }\n          bindings.push(expressionRanges);\n        } while (hasMoreVlq(reader, semi));\n      }\n      range.bindings = bindings;\n\n      ranges.push(range);\n      stack.push(range);\n    }\n\n    genLine++;\n    reader.pos = semi + 1;\n  } while (reader.pos < length);\n\n  return ranges;\n}\n\nexport function encodeGeneratedRanges(ranges: GeneratedRange[]): string {\n  if (ranges.length === 0) return '';\n\n  const writer = new StringWriter();\n\n  for (let i = 0; i < ranges.length; ) {\n    i = _encodeGeneratedRanges(ranges, i, writer, [0, 0, 0, 0, 0, 0, 0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeGeneratedRanges(\n  ranges: GeneratedRange[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenLine\n    number, // GenColumn\n    number, // DefSourcesIndex\n    number, // DefScopesIndex\n    number, // CallSourcesIndex\n    number, // CallLine\n    number, // CallColumn\n  ],\n): number {\n  const range = ranges[index];\n  const {\n    0: startLine,\n    1: startColumn,\n    2: endLine,\n    3: endColumn,\n    isScope,\n    callsite,\n    bindings,\n  } = range;\n\n  if (state[0] < startLine) {\n    catchupLine(writer, state[0], startLine);\n    state[0] = startLine;\n    state[1] = 0;\n  } else if (index > 0) {\n    writer.write(comma);\n  }\n\n  state[1] = encodeInteger(writer, range[1], state[1]);\n\n  const fields =\n    (range.length === 6 ? 0b0001 : 0) | (callsite ? 0b0010 : 0) | (isScope ? 0b0100 : 0);\n  encodeInteger(writer, fields, 0);\n\n  if (range.length === 6) {\n    const { 4: sourcesIndex, 5: scopesIndex } = range;\n    if (sourcesIndex !== state[2]) {\n      state[3] = 0;\n    }\n    state[2] = encodeInteger(writer, sourcesIndex, state[2]);\n    state[3] = encodeInteger(writer, scopesIndex, state[3]);\n  }\n\n  if (callsite) {\n    const { 0: sourcesIndex, 1: callLine, 2: callColumn } = range.callsite!;\n    if (sourcesIndex !== state[4]) {\n      state[5] = 0;\n      state[6] = 0;\n    } else if (callLine !== state[5]) {\n      state[6] = 0;\n    }\n    state[4] = encodeInteger(writer, sourcesIndex, state[4]);\n    state[5] = encodeInteger(writer, callLine, state[5]);\n    state[6] = encodeInteger(writer, callColumn, state[6]);\n  }\n\n  if (bindings) {\n    for (const binding of bindings) {\n      if (binding.length > 1) encodeInteger(writer, -binding.length, 0);\n      const expression = binding[0][0];\n      encodeInteger(writer, expression, 0);\n      let bindingStartLine = startLine;\n      let bindingStartColumn = startColumn;\n      for (let i = 1; i < binding.length; i++) {\n        const expRange = binding[i];\n        bindingStartLine = encodeInteger(writer, expRange[1]!, bindingStartLine);\n        bindingStartColumn = encodeInteger(writer, expRange[2]!, bindingStartColumn);\n        encodeInteger(writer, expRange[0]!, 0);\n      }\n    }\n  }\n\n  for (index++; index < ranges.length; ) {\n    const next = ranges[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeGeneratedRanges(ranges, index, writer, state);\n  }\n\n  if (state[0] < endLine) {\n    catchupLine(writer, state[0], endLine);\n    state[0] = endLine;\n    state[1] = 0;\n  } else {\n    writer.write(comma);\n  }\n  state[1] = encodeInteger(writer, endColumn, state[1]);\n\n  return index;\n}\n\nfunction catchupLine(writer: StringWriter, lastLine: number, line: number) {\n  do {\n    writer.write(semicolon);\n  } while (++lastLine < line);\n}\n", "import { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\nimport { StringWriter, StringReader } from './strings';\n\nexport {\n  decodeOriginalScopes,\n  encodeOriginalScopes,\n  decodeGeneratedRanges,\n  encodeGeneratedRanges,\n} from './scopes';\nexport type { OriginalScope, GeneratedRange, CallSite, BindingExpressionRange } from './scopes';\n\nexport type SourceMapSegment =\n  | [number]\n  | [number, number, number, number]\n  | [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nexport function decode(mappings: string): SourceMapMappings {\n  const { length } = mappings;\n  const reader = new StringReader(mappings);\n  const decoded: SourceMapMappings = [];\n  let genColumn = 0;\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    const line: SourceMapLine = [];\n    let sorted = true;\n    let lastCol = 0;\n    genColumn = 0;\n\n    while (reader.pos < semi) {\n      let seg: SourceMapSegment;\n\n      genColumn = decodeInteger(reader, genColumn);\n      if (genColumn < lastCol) sorted = false;\n      lastCol = genColumn;\n\n      if (hasMoreVlq(reader, semi)) {\n        sourcesIndex = decodeInteger(reader, sourcesIndex);\n        sourceLine = decodeInteger(reader, sourceLine);\n        sourceColumn = decodeInteger(reader, sourceColumn);\n\n        if (hasMoreVlq(reader, semi)) {\n          namesIndex = decodeInteger(reader, namesIndex);\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex];\n        } else {\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn];\n        }\n      } else {\n        seg = [genColumn];\n      }\n\n      line.push(seg);\n      reader.pos++;\n    }\n\n    if (!sorted) sort(line);\n    decoded.push(line);\n    reader.pos = semi + 1;\n  } while (reader.pos <= length);\n\n  return decoded;\n}\n\nfunction sort(line: SourceMapSegment[]) {\n  line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[0] - b[0];\n}\n\nexport function encode(decoded: SourceMapMappings): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string {\n  const writer = new StringWriter();\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    if (i > 0) writer.write(semicolon);\n    if (line.length === 0) continue;\n\n    let genColumn = 0;\n\n    for (let j = 0; j < line.length; j++) {\n      const segment = line[j];\n      if (j > 0) writer.write(comma);\n\n      genColumn = encodeInteger(writer, segment[0], genColumn);\n\n      if (segment.length === 1) continue;\n      sourcesIndex = encodeInteger(writer, segment[1], sourcesIndex);\n      sourceLine = encodeInteger(writer, segment[2], sourceLine);\n      sourceColumn = encodeInteger(writer, segment[3], sourceColumn);\n\n      if (segment.length === 4) continue;\n      namesIndex = encodeInteger(writer, segment[4], namesIndex);\n    }\n  }\n\n  return writer.flush();\n}\n"], "names": [], "mappings": "AAEO,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAE3C,MAAM,KAAK,GAAG,kEAAkE,CAAC;AACjF,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACrC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACrC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAClB;SAEe,aAAa,CAAC,MAAoB,EAAE,QAAgB;IAClE,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,GAAG;QACD,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACvB,KAAK,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC;QACjC,KAAK,IAAI,CAAC,CAAC;KACZ,QAAQ,OAAO,GAAG,EAAE,EAAE;IAEvB,MAAM,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;IAC/B,KAAK,MAAM,CAAC,CAAC;IAEb,IAAI,YAAY,EAAE;QAChB,KAAK,GAAG,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC;KAC9B;IAED,OAAO,QAAQ,GAAG,KAAK,CAAC;AAC1B,CAAC;SAEe,aAAa,CAAC,OAAqB,EAAE,GAAW,EAAE,QAAgB;IAChF,IAAI,KAAK,GAAG,GAAG,GAAG,QAAQ,CAAC;IAE3B,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;IACnD,GAAG;QACD,IAAI,OAAO,GAAG,KAAK,GAAG,QAAQ,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,CAAC;YAAE,OAAO,IAAI,QAAQ,CAAC;QACnC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;KACnC,QAAQ,KAAK,GAAG,CAAC,EAAE;IAEpB,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,UAAU,CAAC,MAAoB,EAAE,GAAW;IAC1D,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;QAAE,OAAO,KAAK,CAAC;IACpC,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC;AACjC;;ACtDA,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAE5B;AACA,MAAM,EAAE,GACN,OAAO,WAAW,KAAK,WAAW;sBACd,IAAI,WAAW,EAAE;MACjC,OAAO,MAAM,KAAK,WAAW;UAC7B;YACE,MAAM,CAAC,GAAe;gBACpB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;gBACpE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;aACvB;SACF;UACD;YACE,MAAM,CAAC,GAAe;gBACpB,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC;gBACD,OAAO,GAAG,CAAC;aACZ;SACF,CAAC;MAEK,YAAY;IAAzB;QACE,QAAG,GAAG,CAAC,CAAC;QACA,QAAG,GAAG,EAAE,CAAC;QACT,WAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;KAe5C;IAbC,KAAK,CAAC,CAAS;QACb,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE;YAC1B,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;SACd;KACF;IAED,KAAK;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAClC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;KACjE;CACF;MAEY,YAAY;IAIvB,YAAY,MAAc;QAH1B,QAAG,GAAG,CAAC,CAAC;QAIN,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACtB;IAED,IAAI;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;KAC3C;IAED,IAAI;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACzC;IAED,OAAO,CAAC,IAAY;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACtC,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;KACzC;;;AC5DH,MAAM,KAAK,GAAU,EAAE,CAAC;SA+BR,oBAAoB,CAAC,KAAa;IAChD,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;IACvC,MAAM,MAAM,GAAoB,EAAE,CAAC;IACnC,MAAM,KAAK,GAAoB,EAAE,CAAC;IAClC,IAAI,IAAI,GAAG,CAAC,CAAC;IAEb,OAAO,MAAM,CAAC,GAAG,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE;QACxC,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;YAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACf,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YACjB,SAAS;SACV;QAED,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;QAEhC,MAAM,KAAK,IACT,OAAO,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAC3E,CAAC;QAEnB,IAAI,IAAI,GAAU,KAAK,CAAC;QACxB,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YAC9B,IAAI,GAAG,EAAE,CAAC;YACV,GAAG;gBACD,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACtB,QAAQ,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;SACtC;QACD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;SAEe,oBAAoB,CAAC,MAAuB;IAC1D,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;IAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAI;QACnC,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACnD;IAED,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;AACxB,CAAC;AAED,SAAS,qBAAqB,CAC5B,MAAuB,EACvB,KAAa,EACb,MAAoB,EACpB,KAEC;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;IAExF,IAAI,KAAK,GAAG,CAAC;QAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEnC,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;IACtC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAE/B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;IAC/C,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE3D,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;QACpB,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAC7B;IAED,KAAK,KAAK,EAAE,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,GAAI;QACrC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,GAAG,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE;YACpD,MAAM;SACP;QACD,KAAK,GAAG,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;KAC7D;IAED,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACpB,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IAEpC,OAAO,KAAK,CAAC;AACf,CAAC;SAEe,qBAAqB,CAAC,KAAa;IACjD,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;IACvC,MAAM,MAAM,GAAqB,EAAE,CAAC;IACpC,MAAM,KAAK,GAAqB,EAAE,CAAC;IAEnC,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,sBAAsB,GAAG,CAAC,CAAC;IAC/B,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,GAAG;QACD,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE;YACtC,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAE7C,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;gBAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;gBAClB,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gBACpB,SAAS;aACV;YAED,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;YACtC,MAAM,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;YAEjC,IAAI,QAAQ,GAAoB,IAAI,CAAC;YACrC,IAAI,QAAQ,GAAc,KAAK,CAAC;YAChC,IAAI,KAAqB,CAAC;YAC1B,IAAI,aAAa,EAAE;gBACjB,MAAM,eAAe,GAAG,aAAa,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC;gBACtE,oBAAoB,GAAG,aAAa,CAClC,MAAM,EACN,sBAAsB,KAAK,eAAe,GAAG,oBAAoB,GAAG,CAAC,CACtE,CAAC;gBAEF,sBAAsB,GAAG,eAAe,CAAC;gBACzC,KAAK,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE,oBAAoB,CAAmB,CAAC;aAC7F;iBAAM;gBACL,KAAK,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAmB,CAAC;aACtD;YAED,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC;YAE3B,IAAI,WAAW,EAAE;gBACf,MAAM,OAAO,GAAG,oBAAoB,CAAC;gBACrC,MAAM,QAAQ,GAAG,YAAY,CAAC;gBAC9B,oBAAoB,GAAG,aAAa,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;gBACnE,MAAM,UAAU,GAAG,OAAO,KAAK,oBAAoB,CAAC;gBACpD,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;gBACpE,cAAc,GAAG,aAAa,CAC5B,MAAM,EACN,UAAU,IAAI,QAAQ,KAAK,YAAY,GAAG,cAAc,GAAG,CAAC,CAC7D,CAAC;gBAEF,QAAQ,GAAG,CAAC,oBAAoB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;aACjE;YACD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE1B,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBAC5B,QAAQ,GAAG,EAAE,CAAC;gBACd,GAAG;oBACD,WAAW,GAAG,OAAO,CAAC;oBACtB,aAAa,GAAG,SAAS,CAAC;oBAC1B,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAClD,IAAI,gBAA0C,CAAC;oBAC/C,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE;wBACzB,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE;4BAC1C,MAAM,MAAM,GAAG,WAAW,CAAC;4BAC3B,WAAW,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;4BACjD,aAAa,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,KAAK,MAAM,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC;4BAClF,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;4BAC5C,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;yBACjE;qBACF;yBAAM;wBACL,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;qBACzC;oBACD,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBACjC,QAAQ,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;aACpC;YACD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnB;QAED,OAAO,EAAE,CAAC;QACV,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;KACvB,QAAQ,MAAM,CAAC,GAAG,GAAG,MAAM,EAAE;IAE9B,OAAO,MAAM,CAAC;AAChB,CAAC;SAEe,qBAAqB,CAAC,MAAwB;IAC5D,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAEnC,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;IAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAI;QACnC,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACtE;IAED,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAC7B,MAAwB,EACxB,KAAa,EACb,MAAoB,EACpB,KAQC;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,MAAM,EACJ,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,WAAW,EACd,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,SAAS,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,GACT,GAAG,KAAK,CAAC;IAEV,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE;QACxB,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;QACrB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACd;SAAM,IAAI,KAAK,GAAG,CAAC,EAAE;QACpB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,MAAM,MAAM,GACV,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,CAAC,KAAK,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC,IAAI,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACvF,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAEjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAClD,IAAI,YAAY,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;YAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;QACD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACzD;IAED,IAAI,QAAQ,EAAE;QACZ,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC,QAAS,CAAC;QACxE,IAAI,YAAY,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;YAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;aAAM,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;YAChC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;QACD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACxD;IAED,IAAI,QAAQ,EAAE;QACZ,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;gBAAE,aAAa,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAClE,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;YACrC,IAAI,gBAAgB,GAAG,SAAS,CAAC;YACjC,IAAI,kBAAkB,GAAG,WAAW,CAAC;YACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC5B,gBAAgB,GAAG,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAE,EAAE,gBAAgB,CAAC,CAAC;gBACzE,kBAAkB,GAAG,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAE,EAAE,kBAAkB,CAAC,CAAC;gBAC7E,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,CAAC;aACxC;SACF;KACF;IAED,KAAK,KAAK,EAAE,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,GAAI;QACrC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,GAAG,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE;YACpD,MAAM;SACP;QACD,KAAK,GAAG,sBAAsB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;KAC9D;IAED,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE;QACtB,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACvC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QACnB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACd;SAAM;QACL,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACrB;IACD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,WAAW,CAAC,MAAoB,EAAE,QAAgB,EAAE,IAAY;IACvE,GAAG;QACD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KACzB,QAAQ,EAAE,QAAQ,GAAG,IAAI,EAAE;AAC9B;;SCtUgB,MAAM,CAAC,QAAgB;IACrC,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAC5B,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC1C,MAAM,OAAO,GAAsB,EAAE,CAAC;IACtC,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,GAAG;QACD,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,IAAI,GAAkB,EAAE,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,SAAS,GAAG,CAAC,CAAC;QAEd,OAAO,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE;YACxB,IAAI,GAAqB,CAAC;YAE1B,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC7C,IAAI,SAAS,GAAG,OAAO;gBAAE,MAAM,GAAG,KAAK,CAAC;YACxC,OAAO,GAAG,SAAS,CAAC;YAEpB,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBAC5B,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gBACnD,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/C,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gBAEnD,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;oBAC5B,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBAC/C,GAAG,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;iBACvE;qBAAM;oBACL,GAAG,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;iBAC3D;aACF;iBAAM;gBACL,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;aACnB;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,GAAG,EAAE,CAAC;SACd;QAED,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;KACvB,QAAQ,MAAM,CAAC,GAAG,IAAI,MAAM,EAAE;IAE/B,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,IAAI,CAAC,IAAwB;IACpC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,cAAc,CAAC,CAAmB,EAAE,CAAmB;IAC9D,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC;SAIe,MAAM,CAAC,OAAoC;IACzD,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;IAClC,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC;YAAE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,SAAS;QAEhC,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC;gBAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE/B,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YACnC,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YAC/D,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAC3D,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YAE/D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YACnC,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;SAC5D;KACF;IAED,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;AACxB;;;;"}